using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي والاستعادة المحسنة
    /// Enhanced backup and restore service
    /// </summary>
    public class BackupService : IBackupService
    {
        private readonly FishFarmDbContext _context;
        private readonly ILogger<BackupService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public BackupService(FishFarmDbContext context, ILogger<BackupService> logger, IConfiguration configuration)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _connectionString = _configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string not found");
        }

        public async Task<BackupResult> CreateFullBackupAsync(string backupPath, string description = "")
        {
            var startTime = DateTime.Now;
            string tempBackupPath = Path.Combine(Path.GetTempPath(), $"temp_backup_{Guid.NewGuid()}.db");

            try
            {
                _logger.LogInformation("بدء إنشاء نسخة احتياطية كاملة: {BackupPath}", backupPath);

                // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                var backupDir = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDir) && !Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                    _logger.LogInformation("تم إنشاء مجلد النسخ الاحتياطية: {BackupDir}", backupDir);
                }

                // نسخ ملف قاعدة البيانات
                var dbPath = GetDatabasePath();
                if (string.IsNullOrEmpty(dbPath) || !File.Exists(dbPath))
                {
                    throw new FileNotFoundException("ملف قاعدة البيانات غير موجود", dbPath);
                }

                _logger.LogInformation("جاري التحقق من سلامة قاعدة البيانات قبل النسخ الاحتياطي");

                // التحقق من وجود ملف قاعدة البيانات
                if (!File.Exists(dbPath))
                {
                    throw new FileNotFoundException("ملف قاعدة البيانات غير موجود");
                }

                // استخدام ملف مؤقت لتجنب مشاكل القفل
                _logger.LogDebug("نسخ قاعدة البيانات إلى ملف مؤقت: {TempPath}", tempBackupPath);
                File.Copy(dbPath, tempBackupPath, true);

                // التحقق من الملف المؤقت
                if (!File.Exists(tempBackupPath) || new FileInfo(tempBackupPath).Length == 0)
                {
                    throw new IOException("فشل إنشاء نسخة مؤقتة من قاعدة البيانات");
                }

                // نسخ الملف المؤقت إلى وجهة النسخ الاحتياطي
                File.Copy(tempBackupPath, backupPath, true);

                // حساب الحجم والتحقق
                var fileInfo = new FileInfo(backupPath);
                var checksum = await CalculateChecksumAsync(backupPath);
                var duration = DateTime.Now - startTime;

                // إنشاء ملف معلومات النسخة الاحتياطية
                var backupInfo = new BackupInfo
                {
                    FileName = Path.GetFileName(backupPath),
                    FullPath = backupPath,
                    Size = fileInfo.Length,
                    CreatedAt = startTime,
                    Description = description,
                    Type = BackupType.Full,
                    IsEncrypted = false,
                    IsCompressed = false,
                    Checksum = checksum,
                    Version = GetApplicationVersion()
                };

                await SaveBackupInfoAsync(backupPath, backupInfo);

                _logger.LogInformation("تم إنشاء النسخة الاحتياطية بنجاح: {BackupPath}, الحجم: {Size} بايت", backupPath, fileInfo.Length);

                return new BackupResult
                {
                    Success = true,
                    Message = "تم إنشاء النسخة الاحتياطية بنجاح",
                    BackupPath = backupPath,
                    BackupSize = fileInfo.Length,
                    Duration = duration,
                    CreatedAt = startTime,
                    Checksum = checksum,
                    Type = BackupType.Full,
                    IsEncrypted = false,
                    IsCompressed = false
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية: {BackupPath}", backupPath);
                return new BackupResult
                {
                    Success = false,
                    Message = $"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}",
                    BackupPath = backupPath,
                    Duration = DateTime.Now - startTime,
                    CreatedAt = startTime,
                    Type = BackupType.Full
                };
            }
        }

        public async Task<BackupResult> CreateIncrementalBackupAsync(string backupPath, string description = "")
        {
            // للتبسيط، سنقوم بإنشاء نسخة كاملة
            // في التطبيق الحقيقي، يمكن تنفيذ النسخ التزايدي بناءً على تواريخ التعديل
            var result = await CreateFullBackupAsync(backupPath, description);
            if (result.Success)
            {
                result.Type = BackupType.Incremental;
            }
            return result;
        }

        public async Task<BackupResult> CreateCompressedBackupAsync(string backupPath, string description = "", Interfaces.CompressionLevel compressionLevel = Interfaces.CompressionLevel.Optimal)
        {
            var startTime = DateTime.Now;
            try
            {
                _logger.LogInformation("بدء إنشاء نسخة احتياطية مضغوطة: {BackupPath}", backupPath);

                // إنشاء نسخة احتياطية مؤقتة
                var tempBackupPath = Path.GetTempFileName();
                var tempResult = await CreateFullBackupAsync(tempBackupPath, description);
                
                if (!tempResult.Success)
                {
                    return tempResult;
                }

                // ضغط النسخة الاحتياطية
                var compressionLevelValue = compressionLevel switch
                {
                    Interfaces.CompressionLevel.Fastest => System.IO.Compression.CompressionLevel.Fastest,
                    Interfaces.CompressionLevel.SmallestSize => System.IO.Compression.CompressionLevel.SmallestSize,
                    _ => System.IO.Compression.CompressionLevel.Optimal
                };

                using (var originalFileStream = new FileStream(tempBackupPath, FileMode.Open))
                using (var compressedFileStream = new FileStream(backupPath, FileMode.Create))
                using (var compressionStream = new GZipStream(compressedFileStream, compressionLevelValue))
                {
                    await originalFileStream.CopyToAsync(compressionStream);
                }

                // حذف الملف المؤقت
                File.Delete(tempBackupPath);

                // حساب الحجم والتحقق
                var fileInfo = new FileInfo(backupPath);
                var checksum = await CalculateChecksumAsync(backupPath);
                var duration = DateTime.Now - startTime;

                // إنشاء ملف معلومات النسخة الاحتياطية
                var backupInfo = new BackupInfo
                {
                    FileName = Path.GetFileName(backupPath),
                    FullPath = backupPath,
                    Size = fileInfo.Length,
                    CreatedAt = startTime,
                    Description = description,
                    Type = BackupType.Full,
                    IsEncrypted = false,
                    IsCompressed = true,
                    Checksum = checksum,
                    Version = GetApplicationVersion()
                };

                await SaveBackupInfoAsync(backupPath, backupInfo);

                _logger.LogInformation("تم إنشاء النسخة الاحتياطية المضغوطة بنجاح: {BackupPath}, الحجم: {Size} بايت", backupPath, fileInfo.Length);

                return new BackupResult
                {
                    Success = true,
                    Message = "تم إنشاء النسخة الاحتياطية المضغوطة بنجاح",
                    BackupPath = backupPath,
                    BackupSize = fileInfo.Length,
                    Duration = duration,
                    CreatedAt = startTime,
                    Checksum = checksum,
                    Type = BackupType.Full,
                    IsEncrypted = false,
                    IsCompressed = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية المضغوطة: {BackupPath}", backupPath);
                return new BackupResult
                {
                    Success = false,
                    Message = $"خطأ في إنشاء النسخة الاحتياطية المضغوطة: {ex.Message}",
                    BackupPath = backupPath,
                    Duration = DateTime.Now - startTime,
                    CreatedAt = startTime,
                    Type = BackupType.Full,
                    IsCompressed = true
                };
            }
        }

        public async Task<BackupResult> CreateEncryptedBackupAsync(string backupPath, string password, string description = "")
        {
            var startTime = DateTime.Now;
            try
            {
                _logger.LogInformation("بدء إنشاء نسخة احتياطية مشفرة: {BackupPath}", backupPath);

                // إنشاء نسخة احتياطية مؤقتة
                var tempBackupPath = Path.GetTempFileName();
                var tempResult = await CreateFullBackupAsync(tempBackupPath, description);
                
                if (!tempResult.Success)
                {
                    return tempResult;
                }

                // تشفير النسخة الاحتياطية
                await EncryptFileAsync(tempBackupPath, backupPath, password);

                // حذف الملف المؤقت
                File.Delete(tempBackupPath);

                // حساب الحجم والتحقق
                var fileInfo = new FileInfo(backupPath);
                var checksum = await CalculateChecksumAsync(backupPath);
                var duration = DateTime.Now - startTime;

                // إنشاء ملف معلومات النسخة الاحتياطية
                var backupInfo = new BackupInfo
                {
                    FileName = Path.GetFileName(backupPath),
                    FullPath = backupPath,
                    Size = fileInfo.Length,
                    CreatedAt = startTime,
                    Description = description,
                    Type = BackupType.Full,
                    IsEncrypted = true,
                    IsCompressed = false,
                    Checksum = checksum,
                    Version = GetApplicationVersion()
                };

                await SaveBackupInfoAsync(backupPath, backupInfo);

                _logger.LogInformation("تم إنشاء النسخة الاحتياطية المشفرة بنجاح: {BackupPath}, الحجم: {Size} بايت", backupPath, fileInfo.Length);

                return new BackupResult
                {
                    Success = true,
                    Message = "تم إنشاء النسخة الاحتياطية المشفرة بنجاح",
                    BackupPath = backupPath,
                    BackupSize = fileInfo.Length,
                    Duration = duration,
                    CreatedAt = startTime,
                    Checksum = checksum,
                    Type = BackupType.Full,
                    IsEncrypted = true,
                    IsCompressed = false
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية المشفرة: {BackupPath}", backupPath);
                return new BackupResult
                {
                    Success = false,
                    Message = $"خطأ في إنشاء النسخة الاحتياطية المشفرة: {ex.Message}",
                    BackupPath = backupPath,
                    Duration = DateTime.Now - startTime,
                    CreatedAt = startTime,
                    Type = BackupType.Full,
                    IsEncrypted = true
                };
            }
        }

        public async Task<RestoreResult> RestoreBackupAsync(string backupPath, string password = "")
        {
            var startTime = DateTime.Now;
            try
            {
                _logger.LogInformation("بدء استعادة النسخة الاحتياطية: {BackupPath}", backupPath);

                if (!File.Exists(backupPath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود");
                }

                // قراءة معلومات النسخة الاحتياطية
                var backupInfo = await LoadBackupInfoAsync(backupPath);
                
                var restoreFilePath = backupPath;

                // فك التشفير إذا كانت النسخة مشفرة
                if (backupInfo?.IsEncrypted == true)
                {
                    if (string.IsNullOrEmpty(password))
                    {
                        throw new ArgumentException("كلمة المرور مطلوبة لفك تشفير النسخة الاحتياطية");
                    }

                    var tempDecryptedPath = Path.GetTempFileName();
                    await DecryptFileAsync(backupPath, tempDecryptedPath, password);
                    restoreFilePath = tempDecryptedPath;
                }

                // فك الضغط إذا كانت النسخة مضغوطة
                if (backupInfo?.IsCompressed == true)
                {
                    var tempDecompressedPath = Path.GetTempFileName();
                    using (var compressedFileStream = new FileStream(restoreFilePath, FileMode.Open))
                    using (var decompressionStream = new GZipStream(compressedFileStream, CompressionMode.Decompress))
                    using (var decompressedFileStream = new FileStream(tempDecompressedPath, FileMode.Create))
                    {
                        await decompressionStream.CopyToAsync(decompressedFileStream);
                    }

                    if (restoreFilePath != backupPath)
                    {
                        File.Delete(restoreFilePath);
                    }
                    restoreFilePath = tempDecompressedPath;
                }

                // إغلاق الاتصالات المفتوحة (SQLite لا يحتاج إلى إغلاق صريح)
                // await _context.Database.CloseConnectionAsync();

                // استعادة قاعدة البيانات
                var dbPath = GetDatabasePath();
                if (!string.IsNullOrEmpty(dbPath))
                {
                    // إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                    var currentBackupPath = $"{dbPath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                    if (File.Exists(dbPath))
                    {
                        File.Copy(dbPath, currentBackupPath, true);
                    }

                    // استعادة النسخة الاحتياطية
                    File.Copy(restoreFilePath, dbPath, true);
                }

                // تنظيف الملفات المؤقتة
                if (restoreFilePath != backupPath && File.Exists(restoreFilePath))
                {
                    File.Delete(restoreFilePath);
                }

                var duration = DateTime.Now - startTime;

                _logger.LogInformation("تم استعادة النسخة الاحتياطية بنجاح: {BackupPath}", backupPath);

                return new RestoreResult
                {
                    Success = true,
                    Message = "تم استعادة النسخة الاحتياطية بنجاح",
                    Duration = duration,
                    RestoredAt = DateTime.Now,
                    TablesRestored = 1, // SQLite - ملف واحد
                    RecordsRestored = 0 // سيتم حسابه لاحقاً إذا لزم الأمر
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استعادة النسخة الاحتياطية: {BackupPath}", backupPath);
                return new RestoreResult
                {
                    Success = false,
                    Message = $"خطأ في استعادة النسخة الاحتياطية: {ex.Message}",
                    Duration = DateTime.Now - startTime,
                    RestoredAt = DateTime.Now
                };
            }
        }

        private string GetDatabasePath()
        {
            // استخراج مسار قاعدة البيانات من connection string
            if (_connectionString.Contains("Data Source="))
            {
                var startIndex = _connectionString.IndexOf("Data Source=") + "Data Source=".Length;
                var endIndex = _connectionString.IndexOf(';', startIndex);
                if (endIndex == -1) endIndex = _connectionString.Length;
                
                return _connectionString.Substring(startIndex, endIndex - startIndex).Trim();
            }
            
            return string.Empty;
        }

        private async Task<string> CalculateChecksumAsync(string filePath)
        {
            using var sha256 = SHA256.Create();
            using var stream = File.OpenRead(filePath);
            var hash = await Task.Run(() => sha256.ComputeHash(stream));
            return Convert.ToBase64String(hash);
        }

        private async Task EncryptFileAsync(string inputPath, string outputPath, string password)
        {
            using var aes = Aes.Create();
            aes.Key = DeriveKeyFromPassword(password, aes.KeySize / 8);
            aes.GenerateIV();

            using var inputStream = new FileStream(inputPath, FileMode.Open);
            using var outputStream = new FileStream(outputPath, FileMode.Create);
            
            // كتابة IV في بداية الملف
            await outputStream.WriteAsync(aes.IV, 0, aes.IV.Length);

            using var cryptoStream = new CryptoStream(outputStream, aes.CreateEncryptor(), CryptoStreamMode.Write);
            await inputStream.CopyToAsync(cryptoStream);
        }

        private async Task DecryptFileAsync(string inputPath, string outputPath, string password)
        {
            using var aes = Aes.Create();
            using var inputStream = new FileStream(inputPath, FileMode.Open);
            
            // قراءة IV من بداية الملف
            var iv = new byte[aes.IV.Length];
            await inputStream.ReadAsync(iv, 0, iv.Length);
            aes.IV = iv;
            aes.Key = DeriveKeyFromPassword(password, aes.KeySize / 8);

            using var outputStream = new FileStream(outputPath, FileMode.Create);
            using var cryptoStream = new CryptoStream(inputStream, aes.CreateDecryptor(), CryptoStreamMode.Read);
            await cryptoStream.CopyToAsync(outputStream);
        }

        private byte[] DeriveKeyFromPassword(string password, int keyLength)
        {
            using var rfc2898 = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes("FishFarmSalt"), 10000, HashAlgorithmName.SHA256);
            return rfc2898.GetBytes(keyLength);
        }

        private async Task SaveBackupInfoAsync(string backupPath, BackupInfo backupInfo)
        {
            var infoPath = backupPath + ".info";
            var json = JsonSerializer.Serialize(backupInfo, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(infoPath, json);
        }

        private async Task<BackupInfo?> LoadBackupInfoAsync(string backupPath)
        {
            var infoPath = backupPath + ".info";
            if (!File.Exists(infoPath))
            {
                return null;
            }

            try
            {
                var json = await File.ReadAllTextAsync(infoPath);
                return JsonSerializer.Deserialize<BackupInfo>(json);
            }
            catch
            {
                return null;
            }
        }

        private string GetApplicationVersion()
        {
            return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";
        }

        public async Task<VerificationResult> VerifyBackupAsync(string backupPath, string password = "")
        {
            try
            {
                _logger.LogInformation("بدء التحقق من النسخة الاحتياطية: {BackupPath}", backupPath);

                if (!File.Exists(backupPath))
                {
                    return new VerificationResult
                    {
                        IsValid = false,
                        Message = "ملف النسخة الاحتياطية غير موجود",
                        Issues = new List<string> { "ملف النسخة الاحتياطية غير موجود" }
                    };
                }

                var issues = new List<string>();

                // قراءة معلومات النسخة الاحتياطية
                var backupInfo = await LoadBackupInfoAsync(backupPath);
                if (backupInfo == null)
                {
                    issues.Add("ملف معلومات النسخة الاحتياطية مفقود أو تالف");
                }

                // التحقق من checksum
                var actualChecksum = await CalculateChecksumAsync(backupPath);
                var checksumMatch = backupInfo?.Checksum == actualChecksum;

                if (!checksumMatch)
                {
                    issues.Add("checksum غير متطابق - قد يكون الملف تالفاً");
                }

                // التحقق من إمكانية قراءة الملف
                var structureValid = true;
                try
                {
                    if (backupInfo?.IsEncrypted == true)
                    {
                        if (string.IsNullOrEmpty(password))
                        {
                            issues.Add("كلمة المرور مطلوبة للتحقق من النسخة المشفرة");
                            structureValid = false;
                        }
                        else
                        {
                            // محاولة فك التشفير للتحقق
                            var tempPath = Path.GetTempFileName();
                            try
                            {
                                await DecryptFileAsync(backupPath, tempPath, password);
                                File.Delete(tempPath);
                            }
                            catch
                            {
                                issues.Add("فشل في فك تشفير النسخة الاحتياطية - كلمة المرور قد تكون خاطئة");
                                structureValid = false;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    issues.Add($"خطأ في التحقق من بنية الملف: {ex.Message}");
                    structureValid = false;
                }

                var isValid = issues.Count == 0;

                return new VerificationResult
                {
                    IsValid = isValid,
                    Message = isValid ? "النسخة الاحتياطية صحيحة" : "تم العثور على مشاكل في النسخة الاحتياطية",
                    ExpectedChecksum = backupInfo?.Checksum ?? "",
                    ActualChecksum = actualChecksum,
                    ChecksumMatch = checksumMatch,
                    StructureValid = structureValid,
                    Issues = issues
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من النسخة الاحتياطية: {BackupPath}", backupPath);
                return new VerificationResult
                {
                    IsValid = false,
                    Message = $"خطأ في التحقق: {ex.Message}",
                    Issues = new List<string> { ex.Message }
                };
            }
        }

        public async Task<List<BackupInfo>> GetBackupListAsync(string backupDirectory)
        {
            try
            {
                if (!Directory.Exists(backupDirectory))
                {
                    return new List<BackupInfo>();
                }

                var backupFiles = Directory.GetFiles(backupDirectory, "*.db")
                    .Concat(Directory.GetFiles(backupDirectory, "*.bak"))
                    .Concat(Directory.GetFiles(backupDirectory, "*.backup"))
                    .Concat(Directory.GetFiles(backupDirectory, "*.gz"));

                var backupInfos = new List<BackupInfo>();

                foreach (var backupFile in backupFiles)
                {
                    var backupInfo = await LoadBackupInfoAsync(backupFile);
                    if (backupInfo != null)
                    {
                        backupInfos.Add(backupInfo);
                    }
                    else
                    {
                        // إنشاء معلومات أساسية للملفات بدون ملف معلومات
                        var fileInfo = new FileInfo(backupFile);
                        backupInfos.Add(new BackupInfo
                        {
                            FileName = fileInfo.Name,
                            FullPath = fileInfo.FullName,
                            Size = fileInfo.Length,
                            CreatedAt = fileInfo.CreationTime,
                            Description = "نسخة احتياطية بدون معلومات",
                            Type = BackupType.Full,
                            IsEncrypted = false,
                            IsCompressed = backupFile.EndsWith(".gz"),
                            Version = "غير معروف"
                        });
                    }
                }

                return backupInfos.OrderByDescending(b => b.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على قائمة النسخ الاحتياطية: {BackupDirectory}", backupDirectory);
                return new List<BackupInfo>();
            }
        }

        public async Task<bool> DeleteBackupAsync(string backupPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                }

                // حذف ملف المعلومات أيضاً
                var infoPath = backupPath + ".info";
                if (File.Exists(infoPath))
                {
                    File.Delete(infoPath);
                }

                _logger.LogInformation("تم حذف النسخة الاحتياطية: {BackupPath}", backupPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف النسخة الاحتياطية: {BackupPath}", backupPath);
                return false;
            }
        }

        public async Task<CleanupResult> CleanupOldBackupsAsync(string backupDirectory, int keepDays = 30)
        {
            try
            {
                _logger.LogInformation("بدء تنظيف النسخ الاحتياطية القديمة: {BackupDirectory}, الاحتفاظ بـ {KeepDays} يوم", backupDirectory, keepDays);

                var cutoffDate = DateTime.Now.AddDays(-keepDays);
                var backups = await GetBackupListAsync(backupDirectory);
                var oldBackups = backups.Where(b => b.CreatedAt < cutoffDate).ToList();

                var deletedFiles = new List<string>();
                long spaceFreed = 0;

                foreach (var backup in oldBackups)
                {
                    try
                    {
                        spaceFreed += backup.Size;
                        await DeleteBackupAsync(backup.FullPath);
                        deletedFiles.Add(backup.FileName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "فشل في حذف النسخة الاحتياطية: {BackupPath}", backup.FullPath);
                    }
                }

                _logger.LogInformation("تم تنظيف {Count} نسخة احتياطية قديمة، تم توفير {SpaceFreed} بايت", deletedFiles.Count, spaceFreed);

                return new CleanupResult
                {
                    Success = true,
                    Message = $"تم حذف {deletedFiles.Count} نسخة احتياطية قديمة",
                    FilesDeleted = deletedFiles.Count,
                    SpaceFreed = spaceFreed,
                    DeletedFiles = deletedFiles
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف النسخ الاحتياطية القديمة: {BackupDirectory}", backupDirectory);
                return new CleanupResult
                {
                    Success = false,
                    Message = $"خطأ في التنظيف: {ex.Message}"
                };
            }
        }

        public async Task<bool> ScheduleAutomaticBackupAsync(BackupSchedule schedule)
        {
            try
            {
                // حفظ جدولة النسخ الاحتياطي في ملف إعدادات
                var scheduleJson = JsonSerializer.Serialize(schedule, new JsonSerializerOptions { WriteIndented = true });
                var schedulePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "backup_schedule.json");
                await File.WriteAllTextAsync(schedulePath, scheduleJson);

                _logger.LogInformation("تم جدولة النسخ الاحتياطي التلقائي: {Frequency} في {Time}", schedule.Frequency, schedule.Time);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جدولة النسخ الاحتياطي التلقائي");
                return false;
            }
        }

        public async Task<bool> CancelAutomaticBackupAsync()
        {
            try
            {
                var schedulePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "backup_schedule.json");
                if (File.Exists(schedulePath))
                {
                    File.Delete(schedulePath);
                }

                _logger.LogInformation("تم إلغاء جدولة النسخ الاحتياطي التلقائي");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إلغاء جدولة النسخ الاحتياطي التلقائي");
                return false;
            }
        }

        public async Task<BackupScheduleStatus> GetAutomaticBackupStatusAsync()
        {
            try
            {
                var schedulePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "backup_schedule.json");

                if (!File.Exists(schedulePath))
                {
                    return new BackupScheduleStatus
                    {
                        IsScheduled = false
                    };
                }

                var scheduleJson = await File.ReadAllTextAsync(schedulePath);
                var schedule = JsonSerializer.Deserialize<BackupSchedule>(scheduleJson);

                if (schedule == null || !schedule.Enabled)
                {
                    return new BackupScheduleStatus
                    {
                        IsScheduled = false,
                        Schedule = schedule
                    };
                }

                // حساب موعد النسخة الاحتياطية التالية
                var nextBackup = CalculateNextBackupTime(schedule);

                return new BackupScheduleStatus
                {
                    IsScheduled = true,
                    Schedule = schedule,
                    NextBackup = nextBackup,
                    LastBackupSuccess = true, // يمكن تحسينه لاحقاً
                    LastBackupMessage = "تم بنجاح"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على حالة النسخ الاحتياطي التلقائي");
                return new BackupScheduleStatus
                {
                    IsScheduled = false
                };
            }
        }

        public async Task<DatabaseStatistics> GetDatabaseStatisticsAsync()
        {
            try
            {
                var dbPath = GetDatabasePath();
                var dbSize = File.Exists(dbPath) ? new FileInfo(dbPath).Length : 0;

                var tableStats = new Dictionary<string, long>();
                var recordStats = new Dictionary<string, long>();

                // الحصول على إحصائيات الجداول
                var tableNames = new[]
                {
                    "Ponds", "ProductionCycles", "FeedConsumptions", "FishMortalities",
                    "Employees", "Payrolls", "Accounts", "Transactions", "Inventories"
                };

                long totalRecords = 0;

                foreach (var tableName in tableNames)
                {
                    try
                    {
                        // هذا مثال مبسط - في التطبيق الحقيقي يمكن استخدام استعلامات SQL
                        var count = await GetTableRecordCountAsync(tableName);
                        recordStats[tableName] = count;
                        totalRecords += count;

                        // تقدير حجم الجدول (مبسط)
                        tableStats[tableName] = count * 1024; // تقدير تقريبي
                    }
                    catch
                    {
                        recordStats[tableName] = 0;
                        tableStats[tableName] = 0;
                    }
                }

                return new DatabaseStatistics
                {
                    DatabaseSize = dbSize,
                    TableCount = tableNames.Length,
                    TotalRecords = totalRecords,
                    TableSizes = tableStats,
                    TableRecords = recordStats,
                    LastOptimized = DateTime.Now.AddDays(-7), // مثال
                    FragmentationLevel = 5.2 // مثال
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات قاعدة البيانات");
                return new DatabaseStatistics();
            }
        }

        public async Task<OptimizationResult> OptimizeDatabaseAsync()
        {
            var startTime = DateTime.Now;
            try
            {
                _logger.LogInformation("بدء تحسين قاعدة البيانات");

                var sizeBefore = await GetDatabaseSizeAsync();
                var optimizationSteps = new List<string>();

                // تنفيذ خطوات التحسين
                optimizationSteps.Add("تنفيذ VACUUM");
                await _context.Database.ExecuteSqlRawAsync("VACUUM");

                optimizationSteps.Add("تنفيذ ANALYZE");
                await _context.Database.ExecuteSqlRawAsync("ANALYZE");

                optimizationSteps.Add("إعادة بناء الفهارس");
                await _context.Database.ExecuteSqlRawAsync("REINDEX");

                var sizeAfter = await GetDatabaseSizeAsync();
                var spaceSaved = sizeBefore - sizeAfter;
                var duration = DateTime.Now - startTime;

                _logger.LogInformation("تم تحسين قاعدة البيانات بنجاح، تم توفير {SpaceSaved} بايت", spaceSaved);

                return new OptimizationResult
                {
                    Success = true,
                    Message = "تم تحسين قاعدة البيانات بنجاح",
                    SizeBefore = sizeBefore,
                    SizeAfter = sizeAfter,
                    SpaceSaved = spaceSaved,
                    Duration = duration,
                    OptimizationSteps = optimizationSteps
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين قاعدة البيانات");
                return new OptimizationResult
                {
                    Success = false,
                    Message = $"خطأ في التحسين: {ex.Message}",
                    Duration = DateTime.Now - startTime
                };
            }
        }

        public async Task<ExportResult> ExportDataAsync(string exportPath, ExportFormat format, ExportOptions options)
        {
            // تنفيذ مبسط للتصدير
            var startTime = DateTime.Now;
            try
            {
                _logger.LogInformation("بدء تصدير البيانات إلى {Format}: {ExportPath}", format, exportPath);

                // للتبسيط، سنقوم بنسخ قاعدة البيانات كاملة
                if (format == ExportFormat.Sql)
                {
                    var result = await CreateFullBackupAsync(exportPath, "تصدير البيانات");
                    return new ExportResult
                    {
                        Success = result.Success,
                        Message = result.Message,
                        ExportPath = exportPath,
                        FileSize = result.BackupSize,
                        RecordsExported = 0, // سيتم حسابه لاحقاً
                        Duration = result.Duration
                    };
                }

                throw new NotSupportedException($"تنسيق التصدير {format} غير مدعوم حالياً");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير البيانات: {ExportPath}", exportPath);
                return new ExportResult
                {
                    Success = false,
                    Message = $"خطأ في التصدير: {ex.Message}",
                    Duration = DateTime.Now - startTime
                };
            }
        }

        public async Task<ImportResult> ImportDataAsync(string importPath, ImportOptions options)
        {
            // تنفيذ مبسط للاستيراد
            var startTime = DateTime.Now;
            try
            {
                _logger.LogInformation("بدء استيراد البيانات من: {ImportPath}", importPath);

                if (options.CreateBackupBeforeImport)
                {
                    var backupPath = $"{GetDatabasePath()}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                    await CreateFullBackupAsync(backupPath, "نسخة احتياطية قبل الاستيراد");
                }

                var result = await RestoreBackupAsync(importPath);
                return new ImportResult
                {
                    Success = result.Success,
                    Message = result.Message,
                    RecordsImported = result.RecordsRestored,
                    Duration = result.Duration
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استيراد البيانات: {ImportPath}", importPath);
                return new ImportResult
                {
                    Success = false,
                    Message = $"خطأ في الاستيراد: {ex.Message}",
                    Duration = DateTime.Now - startTime
                };
            }
        }

        private DateTime? CalculateNextBackupTime(BackupSchedule schedule)
        {
            var now = DateTime.Now;
            var today = now.Date;
            var scheduledTime = today.Add(schedule.Time);

            return schedule.Frequency switch
            {
                BackupFrequency.Daily => scheduledTime > now ? scheduledTime : scheduledTime.AddDays(1),
                BackupFrequency.Weekly => CalculateNextWeeklyBackup(schedule, now),
                BackupFrequency.Monthly => CalculateNextMonthlyBackup(schedule, now),
                _ => null
            };
        }

        private DateTime? CalculateNextWeeklyBackup(BackupSchedule schedule, DateTime now)
        {
            if (!schedule.DayOfWeek.HasValue) return null;

            var today = now.Date;
            var daysUntilTarget = ((int)schedule.DayOfWeek.Value - (int)today.DayOfWeek + 7) % 7;
            var targetDate = today.AddDays(daysUntilTarget);
            var scheduledTime = targetDate.Add(schedule.Time);

            return scheduledTime > now ? scheduledTime : scheduledTime.AddDays(7);
        }

        private DateTime? CalculateNextMonthlyBackup(BackupSchedule schedule, DateTime now)
        {
            if (!schedule.DayOfMonth.HasValue) return null;

            var today = now.Date;
            var targetDay = Math.Min(schedule.DayOfMonth.Value, DateTime.DaysInMonth(today.Year, today.Month));
            var targetDate = new DateTime(today.Year, today.Month, targetDay);
            var scheduledTime = targetDate.Add(schedule.Time);

            if (scheduledTime <= now)
            {
                var nextMonth = today.AddMonths(1);
                targetDay = Math.Min(schedule.DayOfMonth.Value, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month));
                targetDate = new DateTime(nextMonth.Year, nextMonth.Month, targetDay);
                scheduledTime = targetDate.Add(schedule.Time);
            }

            return scheduledTime;
        }

        private async Task<long> GetDatabaseSizeAsync()
        {
            var dbPath = GetDatabasePath();
            return File.Exists(dbPath) ? new FileInfo(dbPath).Length : 0;
        }

        private async Task<long> GetTableRecordCountAsync(string tableName)
        {
            try
            {
                // استعلام مبسط للحصول على عدد السجلات
                var sql = $"SELECT COUNT(*) FROM {tableName}";
                // استخدام طريقة مختلفة للحصول على العدد
                using var command = _context.Database.GetDbConnection().CreateCommand();
                command.CommandText = sql;
                await _context.Database.OpenConnectionAsync();
                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt64(result ?? 0);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// التحقق من سلامة النسخة الاحتياطية
        /// Verify backup integrity
        /// </summary>
        public async Task<bool> VerifyBackupIntegrityAsync(string backupPath)
        {
            try
            {
                _logger.LogInformation("بدء التحقق من سلامة النسخة الاحتياطية: {BackupPath}", backupPath);

                if (!File.Exists(backupPath))
                {
                    _logger.LogError("ملف النسخة الاحتياطية غير موجود: {BackupPath}", backupPath);
                    return false;
                }

                // التحقق من إمكانية فتح الملف
                using var connection = new Microsoft.Data.Sqlite.SqliteConnection($"Data Source={backupPath}");
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA integrity_check";
                var result = await command.ExecuteScalarAsync();

                var isValid = result?.ToString() == "ok";

                if (isValid)
                {
                    _logger.LogInformation("النسخة الاحتياطية سليمة: {BackupPath}", backupPath);
                }
                else
                {
                    _logger.LogError("النسخة الاحتياطية تالفة: {BackupPath}", backupPath);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من سلامة النسخة الاحتياطية: {BackupPath}", backupPath);
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية مضغوطة تلقائياً
        /// Create automatically compressed backup
        /// </summary>
        public async Task<BackupResult> CreateAutoCompressedBackupAsync(string backupPath, string description = "")
        {
            try
            {
                _logger.LogInformation("بدء إنشاء نسخة احتياطية مضغوطة تلقائياً: {BackupPath}", backupPath);

                // إنشاء نسخة احتياطية مؤقتة
                var tempPath = Path.GetTempFileName();
                var tempResult = await CreateFullBackupAsync(tempPath, description);

                if (!tempResult.Success)
                {
                    return tempResult;
                }

                // ضغط النسخة الاحتياطية
                using (var archive = ZipFile.Open(backupPath, ZipArchiveMode.Create))
                {
                    archive.CreateEntryFromFile(tempPath, "database.db", CompressionLevel.Optimal);
                }

                // حذف الملف المؤقت
                File.Delete(tempPath);

                // حساب الحجم الجديد
                var compressedSize = new FileInfo(backupPath).Length;
                var originalSize = tempResult.Size;
                var compressionRatio = originalSize > 0 ? (double)compressedSize / originalSize : 1.0;

                _logger.LogInformation("تم ضغط النسخة الاحتياطية بنسبة: {Ratio:P2}", 1 - compressionRatio);

                return new BackupResult
                {
                    Success = true,
                    Message = "تم إنشاء النسخة الاحتياطية المضغوطة بنجاح",
                    BackupPath = backupPath,
                    Size = compressedSize,
                    Type = BackupType.Full,
                    CreatedAt = DateTime.Now,
                    Duration = tempResult.Duration
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية المضغوطة: {BackupPath}", backupPath);
                return new BackupResult
                {
                    Success = false,
                    Message = $"خطأ في إنشاء النسخة الاحتياطية المضغوطة: {ex.Message}",
                    BackupPath = backupPath
                };
            }
        }
    }
}
