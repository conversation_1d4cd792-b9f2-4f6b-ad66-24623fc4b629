using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة المحتوى
    /// Content management service
    /// </summary>
    public class ContentService : IContentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ContentService> _logger;

        public ContentService(IUnitOfWork unitOfWork, ILogger<ContentService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        // سيتم وضع منطق العمل الخاص بخدمة المحتوى هنا
    }
}