using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Controls;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج محسن لإدخال بيانات الموظفين
    /// Enhanced employee input form
    /// </summary>
    public partial class EnhancedEmployeeForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IEmployeeService _employeeService;
        private readonly Employee? _existingEmployee;
        private readonly bool _isEditMode;

        // Enhanced Controls
        private TextBox fullNameTextBox;
        private TextBox nationalityTextBox;
        private TextBox residenceNumberTextBox;
        private TextBox positionTextBox;
        private EnhancedDateTimePicker joinDatePicker;
        private EnhancedDateTimePicker leaveDatePicker;
        private CheckBox hasLeaveDateCheckBox;
        private NumericUpDown baseSalaryNumericUpDown;
        private ComboBox statusComboBox;
        private TextBox phoneTextBox;
        private TextBox emailTextBox;
        private TextBox addressTextBox;
        private TextBox nationalIdTextBox;
        private EnhancedDateTimePicker birthDatePicker;
        private CheckBox hasBirthDateCheckBox;
        private ComboBox maritalStatusComboBox;
        private NumericUpDown numberOfChildrenNumericUpDown;
        private Button saveButton;
        private Button cancelButton;
        private Label validationLabel;

        // Validation
        private bool isFormValid = false;

        public EnhancedEmployeeForm(IServiceProvider serviceProvider, Employee? existingEmployee = null)
        {
            _serviceProvider = serviceProvider;
            _employeeService = serviceProvider.GetRequiredService<IEmployeeService>();
            _existingEmployee = existingEmployee;
            _isEditMode = existingEmployee != null;

            InitializeComponent();
            SetupControls();
            SetupValidation();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "تعديل بيانات موظف" : "إضافة موظف جديد";
            this.Size = new Size(700, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(245, 245, 245);
        }

        private void SetupControls()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            int y = 20;
            const int spacing = 35;
            const int labelWidth = 120;
            const int controlWidth = 250;

            // Title
            var titleLabel = new Label
            {
                Text = _isEditMode ? "تعديل بيانات موظف" : "إضافة موظف جديد",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(400, 30),
                Location = new Point(150, y),
                TextAlign = ContentAlignment.MiddleCenter
            };
            y += 50;

            // Personal Information Section
            var personalSectionLabel = CreateSectionLabel("المعلومات الشخصية", new Point(20, y));
            y += 35;

            // Full Name
            var fullNameLabel = CreateLabel("الاسم الكامل:", new Point(450, y), labelWidth);
            fullNameTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                MaxLength = 100
            };
            fullNameTextBox.TextChanged += ValidateForm;
            y += spacing;

            // National ID
            var nationalIdLabel = CreateLabel("رقم الهوية:", new Point(450, y), labelWidth);
            nationalIdTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                MaxLength = 20
            };
            nationalIdTextBox.TextChanged += ValidateForm;
            y += spacing;

            // Nationality
            var nationalityLabel = CreateLabel("الجنسية:", new Point(450, y), labelWidth);
            nationalityTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                MaxLength = 50
            };
            y += spacing;

            // Birth Date
            var birthDateLabel = CreateLabel("تاريخ الميلاد:", new Point(450, y), labelWidth);
            hasBirthDateCheckBox = new CheckBox
            {
                Text = "تحديد تاريخ الميلاد",
                Location = new Point(170, y),
                Size = new Size(120, 23),
                CheckAlign = ContentAlignment.MiddleRight
            };
            birthDatePicker = new EnhancedDateTimePicker
            {
                Location = new Point(300, y),
                Size = new Size(120, 25),
                Value = DateTime.Now.AddYears(-25),
                Enabled = false
            };
            hasBirthDateCheckBox.CheckedChanged += (s, e) => birthDatePicker.Enabled = hasBirthDateCheckBox.Checked;
            y += spacing;

            // Phone
            var phoneLabel = CreateLabel("الهاتف:", new Point(450, y), labelWidth);
            phoneTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                MaxLength = 20
            };
            y += spacing;

            // Email
            var emailLabel = CreateLabel("البريد الإلكتروني:", new Point(450, y), labelWidth);
            emailTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                MaxLength = 100
            };
            y += spacing;

            // Address
            var addressLabel = CreateLabel("العنوان:", new Point(450, y), labelWidth);
            addressTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 60),
                Font = new Font("Segoe UI", 9F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                MaxLength = 500
            };
            y += 80;

            // Employment Information Section
            var employmentSectionLabel = CreateSectionLabel("معلومات العمل", new Point(20, y));
            y += 35;

            // Position
            var positionLabel = CreateLabel("المنصب:", new Point(450, y), labelWidth);
            positionTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                MaxLength = 100
            };
            positionTextBox.TextChanged += ValidateForm;
            y += spacing;

            // Join Date
            var joinDateLabel = CreateLabel("تاريخ التوظيف:", new Point(450, y), labelWidth);
            joinDatePicker = new EnhancedDateTimePicker
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Value = DateTime.Now
            };
            y += spacing;

            // Base Salary
            var salaryLabel = CreateLabel("الراتب الأساسي:", new Point(450, y), labelWidth);
            baseSalaryNumericUpDown = new NumericUpDown
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0,
                ThousandsSeparator = true
            };
            baseSalaryNumericUpDown.ValueChanged += ValidateForm;
            y += spacing;

            // Status
            var statusLabel = CreateLabel("الحالة:", new Point(450, y), labelWidth);
            statusComboBox = new ComboBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F)
            };
            statusComboBox.Items.AddRange(new[] { "نشط", "متوقف", "مفصول" });
            statusComboBox.SelectedIndex = 0;
            y += spacing;

            // Leave Date
            var leaveDateLabel = CreateLabel("تاريخ الترك:", new Point(450, y), labelWidth);
            hasLeaveDateCheckBox = new CheckBox
            {
                Text = "تحديد تاريخ الترك",
                Location = new Point(170, y),
                Size = new Size(120, 23),
                CheckAlign = ContentAlignment.MiddleRight
            };
            leaveDatePicker = new EnhancedDateTimePicker
            {
                Location = new Point(300, y),
                Size = new Size(120, 25),
                Value = DateTime.Now,
                Enabled = false
            };
            hasLeaveDateCheckBox.CheckedChanged += (s, e) => leaveDatePicker.Enabled = hasLeaveDateCheckBox.Checked;
            y += spacing;

            // Validation Label
            validationLabel = new Label
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 40),
                ForeColor = Color.Red,
                Font = new Font("Segoe UI", 8F),
                Text = ""
            };
            y += 50;

            // Buttons
            saveButton = new Button
            {
                Text = _isEditMode ? "حفظ التعديلات" : "حفظ الموظف",
                Size = new Size(120, 35),
                Location = new Point(300, y),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Enabled = false
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(120, 35),
                Location = new Point(170, y),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            cancelButton.Click += (s, e) => this.Close();

            panel.Controls.AddRange(new Control[]
            {
                titleLabel, personalSectionLabel,
                fullNameLabel, fullNameTextBox,
                nationalIdLabel, nationalIdTextBox,
                nationalityLabel, nationalityTextBox,
                birthDateLabel, hasBirthDateCheckBox, birthDatePicker,
                phoneLabel, phoneTextBox,
                emailLabel, emailTextBox,
                addressLabel, addressTextBox,
                employmentSectionLabel,
                positionLabel, positionTextBox,
                joinDateLabel, joinDatePicker,
                salaryLabel, baseSalaryNumericUpDown,
                statusLabel, statusComboBox,
                leaveDateLabel, hasLeaveDateCheckBox, leaveDatePicker,
                validationLabel, saveButton, cancelButton
            });

            this.Controls.Add(panel);
        }

        private Label CreateLabel(string text, Point location, int width)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(width, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F)
            };
        }

        private Label CreateSectionLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                BackColor = Color.FromArgb(240, 248, 255),
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleCenter
            };
        }

        private void SetupValidation()
        {
            fullNameTextBox.TextChanged += ValidateForm;
            nationalIdTextBox.TextChanged += ValidateForm;
            positionTextBox.TextChanged += ValidateForm;
            baseSalaryNumericUpDown.ValueChanged += ValidateForm;
        }

        private void ValidateForm(object? sender, EventArgs e)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(fullNameTextBox.Text))
                errors.Add("يجب إدخال الاسم الكامل");

            if (string.IsNullOrWhiteSpace(nationalIdTextBox.Text))
                errors.Add("يجب إدخال رقم الهوية");

            if (string.IsNullOrWhiteSpace(positionTextBox.Text))
                errors.Add("يجب إدخال المنصب");

            if (baseSalaryNumericUpDown.Value <= 0)
                errors.Add("يجب إدخال راتب أساسي");

            // Email validation
            if (!string.IsNullOrWhiteSpace(emailTextBox.Text) && !IsValidEmail(emailTextBox.Text))
                errors.Add("البريد الإلكتروني غير صحيح");

            isFormValid = errors.Count == 0;
            saveButton.Enabled = isFormValid;

            validationLabel.Text = errors.Count > 0 ? string.Join("\n", errors) : "";
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void LoadData()
        {
            if (_existingEmployee != null)
            {
                fullNameTextBox.Text = _existingEmployee.FullName;
                nationalIdTextBox.Text = _existingEmployee.NationalId ?? "";
                nationalityTextBox.Text = _existingEmployee.Nationality;
                positionTextBox.Text = _existingEmployee.Position;
                joinDatePicker.Value = _existingEmployee.JoinDate;
                baseSalaryNumericUpDown.Value = _existingEmployee.BaseSalary;
                statusComboBox.Text = _existingEmployee.Status;
                phoneTextBox.Text = _existingEmployee.Phone ?? "";
                emailTextBox.Text = _existingEmployee.Email ?? "";
                addressTextBox.Text = _existingEmployee.Address ?? "";

                if (_existingEmployee.BirthDate.HasValue)
                {
                    hasBirthDateCheckBox.Checked = true;
                    birthDatePicker.Value = _existingEmployee.BirthDate.Value;
                }

                if (_existingEmployee.LeaveDate.HasValue)
                {
                    hasLeaveDateCheckBox.Checked = true;
                    leaveDatePicker.Value = _existingEmployee.LeaveDate.Value;
                }
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            if (!isFormValid) return;

            try
            {
                this.Cursor = Cursors.WaitCursor;
                saveButton.Enabled = false;

                var employee = _existingEmployee ?? new Employee();
                
                employee.FullName = fullNameTextBox.Text.Trim();
                employee.NationalId = nationalIdTextBox.Text.Trim();
                employee.Nationality = nationalityTextBox.Text.Trim();
                employee.Position = positionTextBox.Text.Trim();
                employee.JoinDate = joinDatePicker.Value;
                employee.BaseSalary = baseSalaryNumericUpDown.Value;
                employee.Status = statusComboBox.Text;
                employee.Phone = phoneTextBox.Text.Trim();
                employee.Email = emailTextBox.Text.Trim();
                employee.Address = addressTextBox.Text.Trim();

                if (hasBirthDateCheckBox.Checked)
                    employee.BirthDate = birthDatePicker.Value;

                if (hasLeaveDateCheckBox.Checked)
                    employee.LeaveDate = leaveDatePicker.Value;

                if (_isEditMode)
                {
                    await _employeeService.UpdateAsync(employee);
                    MessageBox.Show("تم تحديث بيانات الموظف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _employeeService.AddAsync(employee);
                    MessageBox.Show("تم إضافة الموظف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات الموظف:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                saveButton.Enabled = true;
            }
        }
    }
}
