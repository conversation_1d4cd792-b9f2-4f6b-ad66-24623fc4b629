﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ù…Ø­Ø³Ù† Ù„Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†
    /// Enhanced inventory management form
    /// </summary>
    public partial class EnhancedInventoryManagementForm : Form
    {
        private readonly IInventoryService _inventoryService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EnhancedInventoryManagementForm> _logger;

        // UI Controls
        private TabControl tabControl;
        private TabPage inventoryTabPage;
        private TabPage movementsTabPage;
        private TabPage alertsTabPage;
        private TabPage reportsTabPage;

        // Inventory Tab Controls
        private DataGridView inventoryDataGridView;
        private ToolStripButton addButton;
        private ToolStripButton editButton;
        private ToolStripButton deleteButton;
        private ToolStripButton refreshButton;
        private ToolStripButton movementButton;
        private TextBox searchTextBox;
        private ComboBox categoryFilterComboBox;
        private ComboBox statusFilterComboBox;
        private ToolStrip toolStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusStripLabel;

        // Movements Tab Controls
        private DataGridView movementsDataGridView;
        private DateTimePicker fromDatePicker;
        private DateTimePicker toDatePicker;
        private Button loadMovementsButton;

        // Alerts Tab Controls
        private DataGridView alertsDataGridView;
        private Label lowStockCountLabel;
        private Label expiredItemsCountLabel;
        private Label totalValueLabel;

        public EnhancedInventoryManagementForm(IInventoryService inventoryService, IUnitOfWork unitOfWork, ILogger<EnhancedInventoryManagementForm> logger)
        {
            _inventoryService = inventoryService ?? throw new ArgumentNullException(nameof(inventoryService));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadInitialDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ† Ø§Ù„Ù…Ø­Ø³Ù†Ø©";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateTabControl();
            CreateInventoryTab();
            CreateMovementsTab();
            CreateAlertsTab();
            CreateReportsTab();
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            inventoryTabPage = new TabPage("Ø§Ù„Ù…Ø®Ø²ÙˆÙ†");
            movementsTabPage = new TabPage("Ø­Ø±ÙƒØ§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†");
            alertsTabPage = new TabPage("Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª");
            reportsTabPage = new TabPage("Ø§Ù„ØªÙ‚Ø§Ø±ÙŠØ±");

            tabControl.TabPages.AddRange(new TabPage[] { 
                inventoryTabPage, movementsTabPage, alertsTabPage, reportsTabPage 
            });

            this.Controls.Add(tabControl);
        }

        private void CreateInventoryTab()
        {
            // Create toolbar
            toolStrip = new ToolStrip
            {
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            addButton = new ToolStripButton("Ø¥Ø¶Ø§ÙØ© ØµÙ†Ù", null, AddItem_Click) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText };
            editButton = new ToolStripButton("ØªØ¹Ø¯ÙŠÙ„", null, EditItem_Click) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText, Enabled = false };
            deleteButton = new ToolStripButton("Ø­Ø°Ù", null, DeleteItem_Click) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText, Enabled = false };
            movementButton = new ToolStripButton("Ø­Ø±ÙƒØ© Ù…Ø®Ø²ÙˆÙ†", null, AddMovement_Click) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText, Enabled = false };
            refreshButton = new ToolStripButton("ØªØ­Ø¯ÙŠØ«", null, RefreshData_Click) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText };

            toolStrip.Items.AddRange(new ToolStripItem[] {
                addButton, editButton, deleteButton, new ToolStripSeparator(),
                movementButton, new ToolStripSeparator(), refreshButton
            });

            // Create filter panel
            var filterPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            var searchLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø­Ø«:",
                Size = new Size(50, 23),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            searchTextBox = new TextBox
            {
                Size = new Size(200, 23),
                Location = new Point(70, 12)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            var categoryLabel = new Label
            {
                Text = "Ø§Ù„ÙØ¦Ø©:",
                Size = new Size(50, 23),
                Location = new Point(290, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            categoryFilterComboBox = new ComboBox
            {
                Size = new Size(150, 23),
                Location = new Point(350, 12),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryFilterComboBox.Items.AddRange(new[] { "Ø§Ù„ÙƒÙ„", "Ø¹Ù„Ù", "Ø£Ø¯ÙˆÙŠØ©", "Ù…Ø¹Ø¯Ø§Øª", "Ù…ÙˆØ§Ø¯ ÙƒÙŠÙ…ÙŠØ§Ø¦ÙŠØ©", "Ø£Ø®Ø±Ù‰" });
            categoryFilterComboBox.SelectedIndex = 0;
            categoryFilterComboBox.SelectedIndexChanged += CategoryFilter_SelectedIndexChanged;

            var statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Size = new Size(50, 23),
                Location = new Point(520, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            statusFilterComboBox = new ComboBox
            {
                Size = new Size(120, 23),
                Location = new Point(580, 12),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusFilterComboBox.Items.AddRange(new[] { "Ø§Ù„ÙƒÙ„", "Ù†Ø´Ø·", "ØºÙŠØ± Ù†Ø´Ø·", "Ù…Ù†ØªÙ‡ÙŠ Ø§Ù„ØµÙ„Ø§Ø­ÙŠØ©" });
            statusFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndexChanged += StatusFilter_SelectedIndexChanged;

            filterPanel.Controls.AddRange(new Control[] {
                searchLabel, searchTextBox, categoryLabel, categoryFilterComboBox,
                statusLabel, statusFilterComboBox
            });

            // Create data grid view
            inventoryDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            CreateInventoryColumns();
            inventoryDataGridView.SelectionChanged += InventoryDataGridView_SelectionChanged;
            inventoryDataGridView.CellDoubleClick += InventoryDataGridView_CellDoubleClick;

            // Create status strip
            statusStrip = new StatusStrip();
            statusStripLabel = new ToolStripStatusLabel("Ø¬Ø§Ù‡Ø²");
            statusStrip.Items.Add(statusStripLabel);

            // Add controls to inventory tab
            inventoryTabPage.Controls.Add(inventoryDataGridView);
            inventoryTabPage.Controls.Add(filterPanel);
            inventoryTabPage.Controls.Add(toolStrip);
            inventoryTabPage.Controls.Add(statusStrip);
        }

        private void CreateInventoryColumns()
        {
            inventoryDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "ItemName", HeaderText = "Ø§Ø³Ù… Ø§Ù„ØµÙ†Ù", DataPropertyName = "ItemName", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "ItemType", HeaderText = "Ø§Ù„Ù†ÙˆØ¹", DataPropertyName = "ItemType", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Quantity", HeaderText = "Ø§Ù„ÙƒÙ…ÙŠØ©", DataPropertyName = "Quantity", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Unit", HeaderText = "Ø§Ù„ÙˆØ­Ø¯Ø©", DataPropertyName = "Unit", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "UnitPrice", HeaderText = "Ø³Ø¹Ø± Ø§Ù„ÙˆØ­Ø¯Ø©", DataPropertyName = "UnitPrice", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalValue", HeaderText = "Ø§Ù„Ù‚ÙŠÙ…Ø© Ø§Ù„Ø¥Ø¬Ù…Ø§Ù„ÙŠØ©", DataPropertyName = "TotalValue", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "MinimumStock", HeaderText = "Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ø¯Ù†Ù‰", DataPropertyName = "MinimumStock", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ExpiryDate", HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø§Ù†ØªÙ‡Ø§Ø¡", DataPropertyName = "ExpiryDate", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©", DataPropertyName = "Status", Width = 100 }
            });

            // Format currency columns
            inventoryDataGridView.Columns["UnitPrice"].DefaultCellStyle.Format = "C2";
            inventoryDataGridView.Columns["TotalValue"].DefaultCellStyle.Format = "C2";
            
            // Format date columns
            inventoryDataGridView.Columns["ExpiryDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
        }

        private void CreateMovementsTab()
        {
            var filterPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            var fromLabel = new Label
            {
                Text = "Ù…Ù† ØªØ§Ø±ÙŠØ®:",
                Size = new Size(60, 23),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            fromDatePicker = new DateTimePicker
            {
                Size = new Size(150, 23),
                Location = new Point(80, 12),
                Value = DateTime.Now.AddMonths(-1)
            };

            var toLabel = new Label
            {
                Text = "Ø¥Ù„Ù‰ ØªØ§Ø±ÙŠØ®:",
                Size = new Size(60, 23),
                Location = new Point(250, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            toDatePicker = new DateTimePicker
            {
                Size = new Size(150, 23),
                Location = new Point(320, 12),
                Value = DateTime.Now
            };

            loadMovementsButton = new Button
            {
                Text = "ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø­Ø±ÙƒØ§Øª",
                Size = new Size(100, 30),
                Location = new Point(490, 10)
            };
            loadMovementsButton.Click += LoadMovements_Click;

            filterPanel.Controls.AddRange(new Control[] {
                fromLabel, fromDatePicker, toLabel, toDatePicker, loadMovementsButton
            });

            movementsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            CreateMovementsColumns();

            movementsTabPage.Controls.Add(movementsDataGridView);
            movementsTabPage.Controls.Add(filterPanel);
        }

        private void CreateMovementsColumns()
        {
            movementsDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "MovementDate", HeaderText = "Ø§Ù„ØªØ§Ø±ÙŠØ®", DataPropertyName = "MovementDate", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ItemName", HeaderText = "Ø§Ù„ØµÙ†Ù", DataPropertyName = "ItemName", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "MovementType", HeaderText = "Ù†ÙˆØ¹ Ø§Ù„Ø­Ø±ÙƒØ©", DataPropertyName = "MovementType", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Quantity", HeaderText = "Ø§Ù„ÙƒÙ…ÙŠØ©", DataPropertyName = "Quantity", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "UnitPrice", HeaderText = "Ø³Ø¹Ø± Ø§Ù„ÙˆØ­Ø¯Ø©", DataPropertyName = "UnitPrice", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalValue", HeaderText = "Ø§Ù„Ù‚ÙŠÙ…Ø©", DataPropertyName = "TotalValue", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "Ù…Ù„Ø§Ø­Ø¸Ø§Øª", DataPropertyName = "Notes", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "CreatedBy", HeaderText = "Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…", DataPropertyName = "CreatedBy", Width = 120 }
            });

            movementsDataGridView.Columns["MovementDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm";
            movementsDataGridView.Columns["UnitPrice"].DefaultCellStyle.Format = "C2";
            movementsDataGridView.Columns["TotalValue"].DefaultCellStyle.Format = "C2";
        }

        private void CreateAlertsTab()
        {
            var summaryPanel = new Panel
            {
                Height = 100,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(245, 245, 245),
                Padding = new Padding(20)
            };

            lowStockCountLabel = new Label
            {
                Text = "Ø§Ù„Ø£ØµÙ†Ø§Ù Ù…Ù†Ø®ÙØ¶Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: 0",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Red,
                Size = new Size(300, 25),
                Location = new Point(20, 10)
            };

            expiredItemsCountLabel = new Label
            {
                Text = "Ø§Ù„Ø£ØµÙ†Ø§Ù Ù…Ù†ØªÙ‡ÙŠØ© Ø§Ù„ØµÙ„Ø§Ø­ÙŠØ©: 0",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Orange,
                Size = new Size(300, 25),
                Location = new Point(20, 40)
            };

            totalValueLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ù‚ÙŠÙ…Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: 0 Ø±ÙŠØ§Ù„",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Green,
                Size = new Size(300, 25),
                Location = new Point(20, 70)
            };

            summaryPanel.Controls.AddRange(new Control[] {
                lowStockCountLabel, expiredItemsCountLabel, totalValueLabel
            });

            alertsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            CreateAlertsColumns();

            alertsTabPage.Controls.Add(alertsDataGridView);
            alertsTabPage.Controls.Add(summaryPanel);
        }

        private void CreateAlertsColumns()
        {
            alertsDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "AlertType", HeaderText = "Ù†ÙˆØ¹ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡", DataPropertyName = "AlertType", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "ItemName", HeaderText = "Ø§Ø³Ù… Ø§Ù„ØµÙ†Ù", DataPropertyName = "ItemName", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "CurrentQuantity", HeaderText = "Ø§Ù„ÙƒÙ…ÙŠØ© Ø§Ù„Ø­Ø§Ù„ÙŠØ©", DataPropertyName = "CurrentQuantity", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "MinimumStock", HeaderText = "Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ø¯Ù†Ù‰", DataPropertyName = "MinimumStock", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ExpiryDate", HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø§Ù†ØªÙ‡Ø§Ø¡", DataPropertyName = "ExpiryDate", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "DaysToExpiry", HeaderText = "Ø£ÙŠØ§Ù… Ù„Ù„Ø§Ù†ØªÙ‡Ø§Ø¡", DataPropertyName = "DaysToExpiry", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Priority", HeaderText = "Ø§Ù„Ø£ÙˆÙ„ÙˆÙŠØ©", DataPropertyName = "Priority", Width = 100 }
            });

            alertsDataGridView.Columns["ExpiryDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
        }

        private void CreateReportsTab()
        {
            var reportsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "ØªÙ‚Ø§Ø±ÙŠØ± Ø§Ù„Ù…Ø®Ø²ÙˆÙ†",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };

            var inventoryReportButton = new Button
            {
                Text = "ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ù…Ø®Ø²ÙˆÙ† Ø§Ù„Ø´Ø§Ù…Ù„",
                Size = new Size(200, 40),
                Location = new Point(20, 70)
            };
            inventoryReportButton.Click += GenerateInventoryReport_Click;

            var movementsReportButton = new Button
            {
                Text = "ØªÙ‚Ø±ÙŠØ± Ø­Ø±ÙƒØ§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†",
                Size = new Size(200, 40),
                Location = new Point(240, 70)
            };
            movementsReportButton.Click += GenerateMovementsReport_Click;

            var valuationReportButton = new Button
            {
                Text = "ØªÙ‚Ø±ÙŠØ± ØªÙ‚ÙŠÙŠÙ… Ø§Ù„Ù…Ø®Ø²ÙˆÙ†",
                Size = new Size(200, 40),
                Location = new Point(460, 70)
            };
            valuationReportButton.Click += GenerateValuationReport_Click;

            var alertsReportButton = new Button
            {
                Text = "ØªÙ‚Ø±ÙŠØ± Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª",
                Size = new Size(200, 40),
                Location = new Point(20, 130)
            };
            alertsReportButton.Click += GenerateAlertsReport_Click;

            reportsPanel.Controls.AddRange(new Control[] {
                titleLabel, inventoryReportButton, movementsReportButton,
                valuationReportButton, alertsReportButton
            });

            reportsTabPage.Controls.Add(reportsPanel);
        }

        private async void LoadInitialDataAsync()
        {
            try
            {
                await LoadInventoryDataAsync();
                await LoadAlertsDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£ÙˆÙ„ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadInventoryDataAsync()
        {
            try
            {
                statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†...";
                this.Cursor = Cursors.WaitCursor;

                var inventoryItems = await _inventoryService.GetActiveItemsAsync();
                var inventoryList = inventoryItems.Select(item => new
                {
                    item.Id,
                    item.ItemName,
                    item.ItemType,
                    item.Quantity,
                    item.Unit,
                    item.UnitPrice,
                    TotalValue = item.Quantity * item.UnitPrice,
                    item.MinimumStock,
                    item.ExpiryDate,
                    item.Status
                }).ToList();

                inventoryDataGridView.DataSource = inventoryList;
                statusStripLabel.Text = $"ØªÙ… ØªØ­Ù…ÙŠÙ„ {inventoryList.Count} ØµÙ†Ù";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusStripLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private async Task LoadAlertsDataAsync()
        {
            try
            {
                var lowStockItems = await _inventoryService.GetLowStockItemsAsync();
                var expiredItems = await _inventoryService.GetExpiredItemsAsync();
                var totalValue = await _inventoryService.CalculateTotalInventoryValueAsync();

                // Update summary labels
                lowStockCountLabel.Text = $"Ø§Ù„Ø£ØµÙ†Ø§Ù Ù…Ù†Ø®ÙØ¶Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: {lowStockItems.Count()}";
                expiredItemsCountLabel.Text = $"Ø§Ù„Ø£ØµÙ†Ø§Ù Ù…Ù†ØªÙ‡ÙŠØ© Ø§Ù„ØµÙ„Ø§Ø­ÙŠØ©: {expiredItems.Count()}";
                totalValueLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ù‚ÙŠÙ…Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: {totalValue:C2}";

                // Create alerts list
                var alerts = new List<object>();

                foreach (var item in lowStockItems)
                {
                    alerts.Add(new
                    {
                        AlertType = "Ù…Ø®Ø²ÙˆÙ† Ù…Ù†Ø®ÙØ¶",
                        ItemName = item.ItemName,
                        CurrentQuantity = item.Quantity,
                        MinimumStock = item.MinimumStock,
                        ExpiryDate = item.ExpiryDate,
                        DaysToExpiry = item.ExpiryDate?.Subtract(DateTime.Now).Days,
                        Priority = "Ø¹Ø§Ù„ÙŠØ©"
                    });
                }

                foreach (var item in expiredItems)
                {
                    alerts.Add(new
                    {
                        AlertType = "Ù…Ù†ØªÙ‡ÙŠ Ø§Ù„ØµÙ„Ø§Ø­ÙŠØ©",
                        ItemName = item.ItemName,
                        CurrentQuantity = item.Quantity,
                        MinimumStock = item.MinimumStock,
                        ExpiryDate = item.ExpiryDate,
                        DaysToExpiry = item.ExpiryDate?.Subtract(DateTime.Now).Days,
                        Priority = "Ø­Ø±Ø¬Ø©"
                    });
                }

                alertsDataGridView.DataSource = alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª");
            }
        }

        // Event Handlers
        private void InventoryDataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            bool hasSelection = inventoryDataGridView.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
            movementButton.Enabled = hasSelection;
        }

        private void InventoryDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditItem_Click(sender, e);
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                // Implementation for filtering inventory data
                // This would filter the DataGridView based on search text and selected filters
                statusStripLabel.Text = "ØªÙ… ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ù…Ø±Ø´Ø­Ø§Øª";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ù…Ø±Ø´Ø­Ø§Øª");
            }
        }

        private async void AddItem_Click(object? sender, EventArgs e)
        {
            try
            {
                var addForm = new InventoryItemAddEditForm(_logger);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadInventoryDataAsync();
                    await LoadAlertsDataAsync();
                    statusStripLabel.Text = "ØªÙ… Ø¥Ø¶Ø§ÙØ© ØµÙ†Ù Ø¬Ø¯ÙŠØ¯ Ø¨Ù†Ø¬Ø§Ø­";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ø¶Ø§ÙØ© ØµÙ†Ù Ø¬Ø¯ÙŠØ¯");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ø¶Ø§ÙØ© Ø§Ù„ØµÙ†Ù: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void EditItem_Click(object? sender, EventArgs e)
        {
            try
            {
                if (inventoryDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± ØµÙ†Ù Ù„Ù„ØªØ¹Ø¯ÙŠÙ„", "ØªÙ†Ø¨ÙŠÙ‡",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = inventoryDataGridView.SelectedRows[0];
                var itemId = (int)selectedRow.Cells["Id"].Value;
                var item = await _unitOfWork.Inventories.GetByIdAsync(itemId);

                if (item != null)
                {
                    // TODO: ØªØ­ÙˆÙŠÙ„ Inventory Ø¥Ù„Ù‰ InventoryItem
                    MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„ØµÙ†Ù", "ØªØ¹Ø¯ÙŠÙ„ ØµÙ†Ù",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // TODO: ØªÙ†ÙÙŠØ° Ø§Ù„ØªØ¹Ø¯ÙŠÙ„ Ø¨Ø¹Ø¯ Ø¥ØµÙ„Ø§Ø­ Ø§Ù„Ù†Ù…Ø§Ø°Ø¬
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„ØµÙ†Ù");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„ØµÙ†Ù: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteItem_Click(object? sender, EventArgs e)
        {
            try
            {
                if (inventoryDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± ØµÙ†Ù Ù„Ù„Ø­Ø°Ù", "ØªÙ†Ø¨ÙŠÙ‡",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = inventoryDataGridView.SelectedRows[0];
                var itemName = selectedRow.Cells["ItemName"].Value.ToString();

                var result = MessageBox.Show(
                    $"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„ØµÙ†Ù '{itemName}'ØŸ\n\nÙ‡Ø°Ø§ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„ØªØ±Ø§Ø¬Ø¹ Ø¹Ù†Ù‡.",
                    "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    var itemId = (int)selectedRow.Cells["Id"].Value;
                    var item = await _unitOfWork.Inventories.GetByIdAsync(itemId);

                    if (item != null)
                    {
                        await _unitOfWork.Inventories.DeleteAsync(item);
                        await _unitOfWork.SaveChangesAsync();

                        await LoadInventoryDataAsync();
                        await LoadAlertsDataAsync();
                        statusStripLabel.Text = $"ØªÙ… Ø­Ø°Ù Ø§Ù„ØµÙ†Ù {itemName} Ø¨Ù†Ø¬Ø§Ø­";
                        MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„ØµÙ†Ù Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„ØµÙ†Ù");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„ØµÙ†Ù: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddMovement_Click(object? sender, EventArgs e)
        {
            try
            {
                if (inventoryDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± ØµÙ†Ù Ù„Ø¥Ø¶Ø§ÙØ© Ø­Ø±ÙƒØ© Ù…Ø®Ø²ÙˆÙ†", "ØªÙ†Ø¨ÙŠÙ‡",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = inventoryDataGridView.SelectedRows[0];
                var itemId = (int)selectedRow.Cells["Id"].Value;
                var itemName = selectedRow.Cells["ItemName"].Value.ToString();

                // Open inventory movement form
                MessageBox.Show($"Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø­Ø±ÙƒØ© Ø§Ù„Ù…Ø®Ø²ÙˆÙ† Ù„Ù„ØµÙ†Ù: {itemName}", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ø¶Ø§ÙØ© Ø­Ø±ÙƒØ© Ù…Ø®Ø²ÙˆÙ†");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ø¶Ø§ÙØ© Ø­Ø±ÙƒØ© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void RefreshData_Click(object? sender, EventArgs e)
        {
            await LoadInventoryDataAsync();
            await LoadAlertsDataAsync();
            statusStripLabel.Text = "ØªÙ… ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
        }

        private async void LoadMovements_Click(object? sender, EventArgs e)
        {
            try
            {
                statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ Ø­Ø±ÙƒØ§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†...";
                this.Cursor = Cursors.WaitCursor;

                var fromDate = fromDatePicker.Value.Date;
                var toDate = toDatePicker.Value.Date.AddDays(1).AddSeconds(-1);

                var movements = await _unitOfWork.InventoryMovements.FindAsync(m =>
                    m.MovementDate >= fromDate && m.MovementDate <= toDate);

                var movementsList = movements.Select(m => new
                {
                    m.MovementDate,
                    ItemName = "ØºÙŠØ± Ù…Ø­Ø¯Ø¯", // TODO: Ø¥Ø¶Ø§ÙØ© Ø¹Ù„Ø§Ù‚Ø© Inventory
                    m.MovementType,
                    m.Quantity,
                    m.UnitPrice,
                    TotalValue = m.Quantity * m.UnitPrice,
                    m.Notes,
                    CreatedBy = "Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…" // Replace with actual user info
                }).ToList();

                movementsDataGridView.DataSource = movementsList;
                statusStripLabel.Text = $"ØªÙ… ØªØ­Ù…ÙŠÙ„ {movementsList.Count} Ø­Ø±ÙƒØ©";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø­Ø±ÙƒØ§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø­Ø±ÙƒØ§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusStripLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø­Ø±ÙƒØ§Øª";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        // Report generation methods
        private void GenerateInventoryReport_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ù…Ø®Ø²ÙˆÙ† Ø§Ù„Ø´Ø§Ù…Ù„", "ØªÙ‚Ø±ÙŠØ±",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for generating comprehensive inventory report
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ù…Ø®Ø²ÙˆÙ†");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateMovementsReport_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± Ø­Ø±ÙƒØ§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†", "ØªÙ‚Ø±ÙŠØ±",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for generating movements report
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ø­Ø±ÙƒØ§Øª");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateValuationReport_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± ØªÙ‚ÙŠÙŠÙ… Ø§Ù„Ù…Ø®Ø²ÙˆÙ†", "ØªÙ‚Ø±ÙŠØ±",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for generating valuation report
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± Ø§Ù„ØªÙ‚ÙŠÙŠÙ…");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateAlertsReport_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª", "ØªÙ‚Ø±ÙŠØ±",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for generating alerts report
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ ØªÙ‚Ø±ÙŠØ± Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}



