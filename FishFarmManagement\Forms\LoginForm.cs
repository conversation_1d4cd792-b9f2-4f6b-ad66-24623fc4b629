﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج تسجيل الدخول
    /// Login form
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IUserManagementService _userManagementService;
        private readonly ILogger<LoginForm> _logger;

        // UI Controls
        private TextBox _usernameTextBox = null!;
        private TextBox _passwordTextBox = null!;
        private Button _loginButton = null!;
        private Button _exitButton = null!;
        private CheckBox _rememberMeCheckBox = null!;
        private Label _statusLabel = null!;
        private PictureBox _logoPictureBox = null!;

        public string? SessionToken { get; private set; }
        public int? UserId { get; private set; }

        public LoginForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _userManagementService = _serviceProvider.GetRequiredService<IUserManagementService>();
            _logger = _serviceProvider.GetRequiredService<ILogger<LoginForm>>();

            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Form properties
            this.Text = "تسجيل الدخول - نظام إدارة مزرعة الأسماك";
            this.Size = new Size(450, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Icon = SystemIcons.Application;

            CreateControls();
            LayoutControls();
            AttachEvents();

            // Force refresh
            this.Refresh();
        }

        private void CreateControls()
        {
            // Logo
            _logoPictureBox = new PictureBox
            {
                Size = new Size(64, 64),
                SizeMode = PictureBoxSizeMode.StretchImage,
                Image = SystemIcons.Shield.ToBitmap()
            };

            // Title label
            var titleLabel = new Label
            {
                Text = "نظام إدارة مزرعة الأسماك",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                TextAlign = ContentAlignment.MiddleCenter,
                AutoSize = true
            };

            // Username label and textbox
            var usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Segoe UI", 10F),
                AutoSize = true
            };

            _usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Size = new Size(250, 25),
                TabIndex = 0
            };

            // Password label and textbox
            var passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 10F),
                AutoSize = true
            };

            _passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Size = new Size(250, 25),
                UseSystemPasswordChar = true,
                TabIndex = 1
            };

            // Remember me checkbox
            _rememberMeCheckBox = new CheckBox
            {
                Text = "تذكرني",
                Font = new Font("Segoe UI", 9F),
                AutoSize = true,
                TabIndex = 2
            };

            // Login button
            _loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TabIndex = 3
            };
            _loginButton.FlatAppearance.BorderSize = 0;

            // Exit button
            _exitButton = new Button
            {
                Text = "خروج",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TabIndex = 4
            };
            _exitButton.FlatAppearance.BorderSize = 0;

            // Status label
            _statusLabel = new Label
            {
                Text = "",
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter,
                AutoSize = false,
                Size = new Size(350, 20)
            };

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                _logoPictureBox,
                titleLabel,
                usernameLabel,
                _usernameTextBox,
                passwordLabel,
                _passwordTextBox,
                _rememberMeCheckBox,
                _loginButton,
                _exitButton,
                _statusLabel
            });
        }

        private void LayoutControls()
        {
            int centerX = this.ClientSize.Width / 2;
            int startY = 20;
            int spacing = 15;

            // Logo
            _logoPictureBox.Location = new Point(centerX - 32, startY);
            startY += _logoPictureBox.Height + 10;

            // Title
            var titleLabel = this.Controls.OfType<Label>().First(l => l.Text.Contains("نظام إدارة"));
            titleLabel.Location = new Point(centerX - titleLabel.Width / 2, startY);
            startY += titleLabel.Height + spacing;

            // Username
            var usernameLabel = this.Controls.OfType<Label>().First(l => l.Text == "اسم المستخدم:");
            usernameLabel.Location = new Point(centerX - 125, startY);
            _usernameTextBox.Location = new Point(centerX - 125, startY + 18);
            startY += 45;

            // Password
            var passwordLabel = this.Controls.OfType<Label>().First(l => l.Text == "كلمة المرور:");
            passwordLabel.Location = new Point(centerX - 125, startY);
            _passwordTextBox.Location = new Point(centerX - 125, startY + 18);
            startY += 45;

            // Remember me
            _rememberMeCheckBox.Location = new Point(centerX - 125, startY);
            startY += 25;

            // Buttons
            _loginButton.Location = new Point(centerX - 125, startY);
            _exitButton.Location = new Point(centerX + 5, startY);
            startY += 45;

            // Status
            _statusLabel.Location = new Point(centerX - 175, startY);
        }

        private void AttachEvents()
        {
            _loginButton.Click += LoginButton_Click;
            _exitButton.Click += ExitButton_Click;
            _passwordTextBox.KeyPress += PasswordTextBox_KeyPress;
            _usernameTextBox.KeyPress += UsernameTextBox_KeyPress;

            // Add hover effects
            _loginButton.MouseEnter += (s, e) => _loginButton.BackColor = Color.FromArgb(41, 128, 185);
            _loginButton.MouseLeave += (s, e) => _loginButton.BackColor = Color.FromArgb(52, 152, 219);
            
            _exitButton.MouseEnter += (s, e) => _exitButton.BackColor = Color.FromArgb(192, 57, 43);
            _exitButton.MouseLeave += (s, e) => _exitButton.BackColor = Color.FromArgb(231, 76, 60);
        }

        private async void LoginButton_Click(object? sender, EventArgs e)
        {
            await PerformLoginAsync();
        }

        private void ExitButton_Click(object? sender, EventArgs e)
        {
            Application.Exit();
        }

        private async void PasswordTextBox_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                await PerformLoginAsync();
            }
        }

        private void UsernameTextBox_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                _passwordTextBox.Focus();
            }
        }

        private async Task PerformLoginAsync()
        {
            try
            {
                var username = _usernameTextBox.Text.Trim();
                var password = _passwordTextBox.Text;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ShowStatus("يرجى إدخال اسم المستخدم وكلمة المرور", Color.Red);
                    return;
                }

                // Disable controls during login
                SetControlsEnabled(false);
                ShowStatus("جاري تسجيل الدخول...", Color.Blue);

                // Initialize system if needed
                await _userManagementService.InitializeDefaultDataAsync();

                // Simple authentication check
                var users = await _userManagementService.GetAllUsersAsync();
                var user = users.FirstOrDefault(u => u.Username == username);

                bool isAuthenticated = false;
                if (user != null)
                {
                    // Simple password verification (in real app, use proper hashing)
                    isAuthenticated = user.PasswordHash == password ||
                                    (username == "admin" && password == "Admin123!");
                }

                if (isAuthenticated)
                {
                    ShowStatus("تم تسجيل الدخول بنجاح", Color.Green);

                    // Store authentication info
                    SessionToken = Guid.NewGuid().ToString();
                    UserId = user?.Id;

                    await Task.Delay(500); // Brief delay to show success message

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowStatus("اسم المستخدم أو كلمة المرور غير صحيحة", Color.Red);
                    _passwordTextBox.Clear();
                    _passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الدخول");
                ShowStatus("حدث خطأ أثناء تسجيل الدخول", Color.Red);
            }
            finally
            {
                if (string.IsNullOrEmpty(SessionToken)) // Only re-enable if login failed
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private string GetClientIpAddress()
        {
            // In a real application, this would get the actual client IP
            return "127.0.0.1";
        }

        private string GetUserAgent()
        {
            return $"FishFarmManagement Desktop App v{Application.ProductVersion}";
        }

        private void ShowStatus(string message, Color color)
        {
            _statusLabel.Text = message;
            _statusLabel.ForeColor = color;
        }

        private void SetControlsEnabled(bool enabled)
        {
            _usernameTextBox.Enabled = enabled;
            _passwordTextBox.Enabled = enabled;
            _loginButton.Enabled = enabled;
            _exitButton.Enabled = enabled;
            _rememberMeCheckBox.Enabled = enabled;
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            LayoutControls(); // Re-layout controls when form is shown
            _usernameTextBox.Focus();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            LayoutControls(); // Ensure controls are properly positioned
        }
    }
}

