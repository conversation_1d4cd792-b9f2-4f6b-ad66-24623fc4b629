﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.BLL.Services.DTOs;

namespace FishFarmManagement.Forms
{
    public partial class UserGuideForm : Form
    {
        private readonly ILogger<UserGuideForm> _logger;
        private readonly IContentService _contentService;
        private TreeView topicsTreeView;
        private RichTextBox contentRichTextBox;
        private TextBox searchTextBox;
        private Button searchButton;
        private int _currentNumberedListIndex = 1;

        public UserGuideForm(IServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetRequiredService<ILogger<UserGuideForm>>();
            _contentService = serviceProvider.GetRequiredService<IContentService>();
            InitializeComponent();
            LoadGuideContent();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø¯Ù„ÙŠÙ„ Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 73, 94),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "Ø¯Ù„ÙŠÙ„ Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù… - Ù†Ø¸Ø§Ù… Ø¥Ø¯Ø§Ø±Ø© Ù…Ø²Ø§Ø±Ø¹ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Search panel
            var searchPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            var searchLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø­Ø«:",
                AutoSize = true,
                Location = new Point(20, 15),
                Font = new Font("Segoe UI", 10F)
            };

            searchTextBox = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(70, 12),
                Font = new Font("Segoe UI", 10F)
            };

            searchButton = new Button
            {
                Text = "Ø¨Ø­Ø«",
                Size = new Size(80, 25),
                Location = new Point(380, 12),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            searchButton.Click += SearchButton_Click;

            searchPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox, searchButton });

            // Main content panel
            var mainPanel = new Panel { Dock = DockStyle.Fill };

            // Split container
            var splitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                SplitterDistance = 300,
                Orientation = Orientation.Vertical
            };

            // Topics tree view
            topicsTreeView = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F),
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                HideSelection = false,
                BackColor = Color.FromArgb(250, 250, 250)
            };
            topicsTreeView.AfterSelect += TopicsTreeView_AfterSelect;

            // Content rich text box
            contentRichTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 11F),
                ReadOnly = true,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Padding = new Padding(20)
            };

            splitContainer.Panel1.Controls.Add(topicsTreeView);
            splitContainer.Panel2.Controls.Add(contentRichTextBox);
            mainPanel.Controls.Add(splitContainer);

            this.Controls.AddRange(new Control[] { mainPanel, searchPanel, headerPanel });
        }

        private async void LoadGuideContent()
        {
            try
            {
                // Clear existing content
                topicsTreeView.Nodes.Clear();

                // Load content index from service
                var contentIndex = await _contentService.GetContentIndexAsync();
                if (contentIndex == null)
                {
                    _logger.LogWarning("Ù„Ù… ÙŠØªÙ… Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ ÙÙ‡Ø±Ø³ Ø§Ù„Ù…Ø­ØªÙˆÙ‰");
                    ShowErrorMessage("Ù„Ù… ÙŠØªÙ… Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ ÙÙ‡Ø±Ø³ Ø§Ù„Ù…Ø­ØªÙˆÙ‰");
                    return;
                }

                // Build tree from content index
                foreach (var topic in contentIndex.Topics.OrderBy(t => t.Order))
                {
                    var node = topicsTreeView.Nodes.Add(topic.Id, topic.Title);
                    node.Tag = topic.Id;

                    if (topic.Children != null)
                    {
                        foreach (var child in topic.Children.OrderBy(c => c.Order))
                        {
                            var childNode = node.Nodes.Add(child.Id, child.Title);
                            childNode.Tag = child.Id;
                        }
                    }
                }

                // Expand main nodes
                topicsTreeView.ExpandAll();

                // Select welcome node by default
                var welcomeNode = topicsTreeView.Nodes.Cast<TreeNode>().FirstOrDefault(n => n.Name == "welcome");
                if (welcomeNode != null)
                {
                    topicsTreeView.SelectedNode = welcomeNode;
                    await ShowContentAsync("welcome");
                }

                _logger.LogInformation("ØªÙ… ØªØ­Ù…ÙŠÙ„ Ù…Ø­ØªÙˆÙ‰ Ø¯Ù„ÙŠÙ„ Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù… Ø¨Ù†Ø¬Ø§Ø­");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ù…Ø­ØªÙˆÙ‰ Ø¯Ù„ÙŠÙ„ Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…");
                ShowErrorMessage($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¯Ù„ÙŠÙ„ Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…: {ex.Message}");
            }
        }

        private async void TopicsTreeView_AfterSelect(object? sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag != null)
            {
                await ShowContentAsync(e.Node.Tag.ToString()!);
            }
        }

        private async Task ShowContentAsync(string topicId)
        {
            try
            {
                _currentNumberedListIndex = 1; // Reset numbering for each topic
                contentRichTextBox.Clear();

                var content = await _contentService.GetTopicContentAsync(topicId);
                if (content == null)
                {
                    ShowDefaultContent(topicId);
                    return;
                }

                // Display title
                AddTitle(content.Title);

                // Display content as paragraphs
                if (!string.IsNullOrEmpty(content.Content))
                {
                    var lines = content.Content.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var line in lines)
                    {
                        AddParagraph(line.Trim());
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¹Ø±Ø¶ Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ù…ÙˆØ¶ÙˆØ¹ {TopicId}", topicId);
                ShowDefaultContent(topicId);
            }
        }

        private void ShowDefaultContent(string topicId)
        {
            AddTitle("Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±");
            AddParagraph($"Ù…Ø­ØªÙˆÙ‰ Ù‡Ø°Ø§ Ø§Ù„Ù‚Ø³Ù… ({topicId}) Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ± ÙˆØ³ÙŠØªÙ… Ø¥Ø¶Ø§ÙØªÙ‡ ÙÙŠ Ø§Ù„ØªØ­Ø¯ÙŠØ«Ø§Øª Ø§Ù„Ù‚Ø§Ø¯Ù…Ø©.");
            AddParagraph("ÙŠØ±Ø¬Ù‰ Ù…Ø±Ø§Ø¬Ø¹Ø© Ø§Ù„Ø£Ù‚Ø³Ø§Ù… Ø§Ù„Ø£Ø®Ø±Ù‰ Ù„Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ù…Ø³Ø§Ø¹Ø¯Ø©.");
        }

        private void AddTitle(string text)
        {
            contentRichTextBox.SelectionFont = new Font("Segoe UI", 16F, FontStyle.Bold);
            contentRichTextBox.SelectionColor = Color.FromArgb(52, 73, 94);
            contentRichTextBox.AppendText(text + "\n\n");
        }

        private void AddSubtitle(string text)
        {
            contentRichTextBox.SelectionFont = new Font("Segoe UI", 13F, FontStyle.Bold);
            contentRichTextBox.SelectionColor = Color.FromArgb(52, 152, 219);
            contentRichTextBox.AppendText(text + "\n");
        }

        private void AddParagraph(string text)
        {
            contentRichTextBox.SelectionFont = new Font("Segoe UI", 11F);
            contentRichTextBox.SelectionColor = Color.Black;
            contentRichTextBox.AppendText(text + "\n\n");
        }

        private void AddBulletPoint(string text)
        {
            contentRichTextBox.SelectionFont = new Font("Segoe UI", 11F);
            contentRichTextBox.SelectionColor = Color.Black;
            contentRichTextBox.AppendText("â€¢ " + text + "\n");
        }

        private void AddNumberedPoint(string text)
        {
            contentRichTextBox.SelectionFont = new Font("Segoe UI", 11F);
            contentRichTextBox.SelectionColor = Color.Black;
            contentRichTextBox.AppendText($"{_currentNumberedListIndex}. {text}\n");
            _currentNumberedListIndex++;
        }

        private async void SearchButton_Click(object? sender, EventArgs e)
        {
            var searchTerm = searchTextBox.Text.Trim();
            if (string.IsNullOrEmpty(searchTerm))
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø¥Ø¯Ø®Ø§Ù„ ÙƒÙ„Ù…Ø© Ø§Ù„Ø¨Ø­Ø«", "ØªÙ†Ø¨ÙŠÙ‡",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // Search using content service directly
                var searchResults = await _contentService.SearchContentAsync(searchTerm);

                if (searchResults.Any())
                {
                    // Show first result for now (simplified)
                    var firstResult = searchResults.First();
                    var node = FindNodeByTag(topicsTreeView.Nodes, firstResult.TopicId);
                    if (node != null)
                    {
                        topicsTreeView.SelectedNode = node;
                        await ShowContentAsync(firstResult.TopicId);
                    }
                    _logger.LogInformation($"تم البحث عن: {searchTerm}");
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على أي نتائج", "نتيجة البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨Ø­Ø«: {SearchTerm}", searchTerm);
                MessageBox.Show("Ø­Ø¯Ø« Ø®Ø·Ø£ Ø£Ø«Ù†Ø§Ø¡ Ø§Ù„Ø¨Ø­Ø«", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private TreeNode? FindNodeByTag(TreeNodeCollection nodes, string tag)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Tag?.ToString() == tag)
                    return node;

                var found = FindNodeByTag(node.Nodes, tag);
                if (found != null)
                    return found;
            }
            return null;
        }

        private void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void AddCodeBlock(string code)
        {
            contentRichTextBox.SelectionFont = new Font("Consolas", 10F);
            contentRichTextBox.SelectionBackColor = Color.FromArgb(245, 245, 245);
            contentRichTextBox.SelectionColor = Color.FromArgb(51, 51, 51);
            contentRichTextBox.AppendText($"{code}\n\n");
        }
    }
}



