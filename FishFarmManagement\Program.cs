using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;
using FishFarmManagement.DAL;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.BLL.Services;
using FishFarmManagement.Forms;
using FishFarmManagement.Models.Configuration;

namespace FishFarmManagement
{
    internal static class Program
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق
        /// The main entry point for the application
        /// </summary>
        [STAThread]
        static async Task Main()
        {
            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("Logs/fishfarm-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            try
            {
                // Configure application
                Application.SetHighDpiMode(HighDpiMode.SystemAware);
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // Build configuration
                var basePath = AppDomain.CurrentDomain.BaseDirectory;
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(basePath)
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Create host builder
                var host = CreateHostBuilder(configuration).Build();

                // Initialize database
                await InitializeDatabaseAsync(host.Services);

                // Start the application
                using (var scope = host.Services.CreateScope())
                {
                    // Show login form first
                    var loginForm = new Forms.LoginForm(scope.ServiceProvider);
                    var loginResult = loginForm.ShowDialog();

                    if (loginResult == DialogResult.OK)
                    {
                        // Login successful, show main form
                        var mainForm = scope.ServiceProvider.GetRequiredService<FishFarmManagement.Forms.MainForm>();
                        Application.Run(mainForm);
                    }
                    // If login was cancelled or failed, application will exit
                }
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "خطأ في بدء تشغيل التطبيق");
                MessageBox.Show($"خطأ في بدء تشغيل التطبيق:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        private static IHostBuilder CreateHostBuilder(IConfiguration configuration)
        {
            return Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    // Add configuration
                    services.AddSingleton(configuration);

                    // Configure strongly-typed settings
                    services.Configure<ApplicationSettings>(configuration.GetSection(ApplicationSettings.SectionName));
                    services.Configure<DatabaseSettings>(configuration.GetSection(DatabaseSettings.SectionName));
                    services.Configure<SecuritySettings>(configuration.GetSection(SecuritySettings.SectionName));
                    services.Configure<ReportsSettings>(configuration.GetSection(ReportsSettings.SectionName));
                    services.Configure<NotificationsSettings>(configuration.GetSection(NotificationsSettings.SectionName));
                    services.Configure<LoggingSettings>(configuration.GetSection(LoggingSettings.SectionName));

                    // Add database context - SQLite only
                    services.AddDbContext<FishFarmDbContext>(options =>
                    {
                        options.UseSqlite(configuration.GetConnectionString("DefaultConnection"),
                            b => b.MigrationsAssembly("FishFarmManagement.DAL"));
                    });

                    // Add repositories and unit of work
                    services.AddScoped<IUnitOfWork, UnitOfWork>();

                    // Add business services
                    services.AddScoped<IPondService, PondService>();
                    services.AddScoped<IAccountingService, AccountingService>();
                    services.AddScoped<IEmployeeService, EmployeeService>();
                    services.AddScoped<IProductionCycleService, ProductionCycleService>();
                    services.AddScoped<IInventoryService, InventoryService>();
                    services.AddScoped<IReportService, ReportService>();
                    services.AddScoped<IFarmInfoService, FarmInfoService>();
                    services.AddScoped<IFeedService, FeedService>();
                    services.AddScoped<IMedicationService, MedicationService>();
                    services.AddScoped<IStatisticsService, StatisticsService>();
                    services.AddScoped<IAuthenticationService, AuthenticationService>();
                    services.AddScoped<IAuthorizationService, AuthorizationService>();
                    services.AddScoped<ISettingsService, SettingsService>();
                    services.AddScoped<IProductionCycleCalculatorService, ProductionCycleCalculatorService>();
                    services.AddScoped<IContentService, ContentService>();
                    services.AddScoped<INotificationService, NotificationService>();
                    services.AddScoped<IBackupService, BackupService>();
                    services.AddScoped<IUserManagementService, UserManagementService>();
                    services.AddScoped<EnhancedReportService>();
                    services.AddScoped<ProfitabilityCalculatorService>();
                    services.AddScoped<EnhancedNotificationService>();
                    services.AddScoped<ILicenseService, LicenseService>();
                    services.AddScoped<ISearchService, SearchService>();

                    // Register helpers
                    services.AddScoped<FishFarmManagement.Helpers.ReportExporter>();

                    // Add forms
                    services.AddTransient<FishFarmManagement.Forms.MainForm>();
                    services.AddTransient<PondManagementForm>();
                    services.AddTransient<ProductionCycleForm>();
                    services.AddTransient<EmployeeManagementForm>();
                    services.AddTransient<AccountingForm>();
                    services.AddTransient<ReportsForm>();
                    services.AddTransient<SettingsForm>();

                    // Add new forms
                    services.AddTransient<BackupRestoreForm>();
                    services.AddTransient<EnhancedBackupForm>();
                    services.AddTransient<AutoBackupManagementForm>();
                    services.AddTransient<InventoryManagementForm>();
                    services.AddTransient<EnhancedInventoryManagementForm>();
                    services.AddTransient<EnhancedAccountingForm>();
                    services.AddTransient<EnhancedDashboardForm>();
                    services.AddTransient<ChartOfAccountsForm>();
                    services.AddTransient<CostCentersForm>();
                    services.AddTransient<ProductionReportsForm>();
                    services.AddTransient<FinancialReportsForm>();
                    services.AddTransient<EmployeeReportsForm>();
                    services.AddTransient<EnhancedReportsForm>();
                    services.AddTransient<FarmInfoForm>();
                    services.AddTransient<DatabaseOptimizationForm>();
                    services.AddTransient<UserGuideForm>();
                    services.AddTransient<LicenseActivationForm>();

                    // Add logging
                    services.AddLogging(builder =>
                    {
                        builder.AddSerilog();
                    });
                });
        }

        private static async Task InitializeDatabaseAsync(IServiceProvider services)
        {
            using var scope = services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<FishFarmDbContext>();
            var configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();
            var loggerFactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("DatabaseInitializer");

            try
            {
                bool useManualScript = configuration.GetValue<bool>("Database:UseManualScript", false);
                bool autoMigrate = configuration.GetValue<bool>("Database:AutoMigrate", true);
                bool databaseExists = await context.Database.CanConnectAsync();

                logger.LogInformation("تهيئة قاعدة البيانات: الحالة الحالية = {0}، التهجير التلقائي = {1}، استخدام نص SQL = {2}",
                                    databaseExists ? "موجودة" : "غير موجودة", autoMigrate, useManualScript);

                if (databaseExists)
                {
                    logger.LogInformation("تم اكتشاف قاعدة بيانات موجودة");
                }
                else
                {
                    // استراتيجية إنشاء قاعدة البيانات
                    if (useManualScript)
                    {
                        // استخدام نص SQL يدويًا (يجب تنفيذه مسبقًا)
                        logger.LogInformation("الاعتماد على إنشاء قاعدة البيانات اليدوي باستخدام نص SQL");

                        // فحص إذا كانت قاعدة البيانات تم إنشاؤها يدويًا
                        if (!await context.Database.CanConnectAsync())
                        {
                            logger.LogWarning("لم يتم العثور على قاعدة بيانات. يُرجى تشغيل النص البرمجي SQL يدويًا أولاً");
                            throw new InvalidOperationException("لم يتم العثور على قاعدة البيانات. يُرجى تشغيل النص البرمجي SQL أولاً.");
                        }
                    }
                    else if (autoMigrate)
                    {
                        // تطبيق التهجيرات لإنشاء قاعدة البيانات
                        logger.LogInformation("إنشاء قاعدة البيانات باستخدام التهجيرات");
                        await context.Database.MigrateAsync();
                        logger.LogInformation("تم تطبيق migrations بنجاح");
                    }
                    else
                    {
                        // إنشاء قاعدة بيانات بدون تهجيرات
                        logger.LogInformation("إنشاء قاعدة البيانات بدون تهجيرات");
                        await context.Database.EnsureCreatedAsync();
                        logger.LogInformation("تم إنشاء قاعدة البيانات بنجاح");
                    }
                }

                // Default user creation is handled by UserManagementService
                // await EnsureDefaultUserAsync(context, logger);

                logger.LogInformation("تم تهيئة قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "خطأ في تهيئة قاعدة البيانات: {0}", ex.Message);

                // إعطاء رسالة خطأ أكثر وضوحًا للمستخدم
                string errorMessage = "حدث خطأ أثناء تهيئة قاعدة البيانات: " + ex.Message;
                if (ex.InnerException != null)
                {
                    errorMessage += "\n\nسبب الخطأ: " + ex.InnerException.Message;
                }

                throw new Exception(errorMessage, ex);
            }
        }

        private static async Task EnsureDefaultUserAsync(FishFarmDbContext context, Microsoft.Extensions.Logging.ILogger logger)
        {
            try
            {
                // Check if admin user exists
                var adminUser = await context.Users.FirstOrDefaultAsync(u => u.Username == "admin");
                if (adminUser == null)
                {
                    // Use fixed default password
                    var tempPassword = "Admin123!";

                    // Create default admin user
                    adminUser = new FishFarmManagement.Models.User
                    {
                        Username = "admin",
                        Email = "<EMAIL>",
                        FullName = "مدير النظام",
                        Status = FishFarmManagement.Models.UserStatus.Active,
                        IsSystemAdmin = true,
                        CreatedDate = DateTime.Now,
                        MustChangePassword = true // إجبار تغيير كلمة المرور عند أول دخول
                    };
                    adminUser.SetPassword(tempPassword);

                    context.Users.Add(adminUser);
                    await context.SaveChangesAsync();

                    logger.LogWarning("تم إنشاء المستخدم الافتراضي 'admin' بكلمة المرور: {TempPassword}", tempPassword);
                    logger.LogWarning("يجب تغيير كلمة المرور فوراً عند أول تسجيل دخول لأسباب أمنية!");

                    // Show message box with default password
                    MessageBox.Show(
                        $"تم إنشاء المستخدم الافتراضي:\n\nاسم المستخدم: admin\nكلمة المرور: {tempPassword}\n\nيجب تغيير كلمة المرور فوراً عند أول تسجيل دخول!",
                        "معلومات المستخدم الافتراضي",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }
                else
                {
                    logger.LogInformation("المستخدم الافتراضي موجود بالفعل - Username: {Username}, Email: {Email}", adminUser.Username, adminUser.Email);

                    // Verify password hash exists
                    if (string.IsNullOrEmpty(adminUser.PasswordHash))
                    {
                        var tempPassword = "Admin123!";
                        logger.LogWarning("كلمة المرور فارغة للمستخدم الافتراضي، سيتم إعادة تعيينها");
                        adminUser.SetPassword(tempPassword);
                        adminUser.MustChangePassword = true;
                        context.Users.Update(adminUser);
                        await context.SaveChangesAsync();
                        logger.LogWarning("تم إعادة تعيين كلمة المرور المؤقتة: {TempPassword}", tempPassword);

                        MessageBox.Show(
                            $"تم إعادة تعيين كلمة المرور للمستخدم الافتراضي:\n\nاسم المستخدم: admin\nكلمة المرور المؤقتة: {tempPassword}\n\nيجب تغيير كلمة المرور فوراً!",
                            "إعادة تعيين كلمة المرور",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);
                    }
                }

                // Log total users count
                var totalUsers = await context.Users.CountAsync();
                logger.LogInformation("إجمالي المستخدمين في قاعدة البيانات: {Count}", totalUsers);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "خطأ في إنشاء المستخدم الافتراضي");
            }
        }

        /// <summary>
        /// إنشاء كلمة مرور مؤقتة عشوائية
        /// Generate a random temporary password
        /// </summary>
        private static string GenerateTemporaryPassword()
        {
            const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
            var random = new Random();
            var password = new char[8];

            for (int i = 0; i < password.Length; i++)
            {
                password[i] = chars[random.Next(chars.Length)];
            }

            return new string(password);
        }
    }
}
